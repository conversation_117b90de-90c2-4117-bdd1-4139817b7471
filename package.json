{"name": "ghs-color-next", "version": "2.0.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "lint:fix": "next lint --fix", "type-check": "tsc --noEmit", "test": "jest", "test:watch": "jest --watch"}, "dependencies": {"@dnd-kit/core": "^6.3.1", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@hookform/resolvers": "^3.3.0", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-dialog": "^1.0.5", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-slot": "^1.0.2", "@radix-ui/react-switch": "^1.0.3", "@radix-ui/react-toast": "^1.1.5", "@types/node": "^20.0.0", "@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "autoprefixer": "^10.4.0", "class-variance-authority": "^0.7.1", "clsx": "^2.0.0", "country-flag-icons": "^1.5.19", "framer-motion": "^10.16.0", "lucide-react": "^0.292.0", "lz-string": "^1.5.0", "next": "^14.0.0", "postcss": "^8.4.0", "qrcode": "^1.5.4", "react": "^18.2.0", "react-dom": "^18.2.0", "react-hook-form": "^7.47.0", "tailwind-merge": "^2.0.0", "tailwindcss": "^3.3.0", "tailwindcss-animate": "^1.0.7", "typescript": "^5.0.0", "zod": "^3.22.0", "zustand": "^4.4.0"}, "devDependencies": {"@commitlint/cli": "^18.0.0", "@commitlint/config-conventional": "^18.0.0", "@testing-library/jest-dom": "^6.0.0", "@testing-library/react": "^13.4.0", "@types/lz-string": "^1.5.0", "@types/qrcode": "^1.5.5", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "eslint": "^8.0.0", "eslint-config-next": "^14.0.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^5.0.0", "husky": "^8.0.0", "jest": "^29.0.0", "jest-environment-jsdom": "^29.0.0", "prettier": "^3.0.0"}}