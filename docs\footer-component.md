# Footer 组件文档

## 概述

Footer 组件是 GHS Color Next 项目的页脚组件，提供了项目信息、快速链接和版权信息的展示。

## 功能特性

### 🎨 设计特性
- **现代化设计**：采用与项目一致的设计风格
- **响应式布局**：在不同屏幕尺寸下都有良好的表现
- **主题适配**：支持明暗主题自动切换
- **毛玻璃效果**：使用 backdrop-blur 实现现代化的视觉效果

### 📱 响应式设计
- **移动端优化**：在小屏幕设备上采用垂直布局
- **桌面端优化**：在大屏幕设备上采用水平布局
- **自适应间距**：根据屏幕尺寸调整内容间距

### 🔗 功能内容
- **项目信息**：显示项目名称、版本号和描述
- **快速链接**：提供文档、问题反馈、贡献指南等链接
- **GitHub 链接**：直接跳转到项目源码仓库
- **版权信息**：显示版权声明和制作团队信息

## 技术实现

### 组件结构
```
Footer
├── 主要内容区域
│   ├── 左侧：项目信息
│   │   ├── 项目标题和版本
│   │   └── 项目描述
│   └── 右侧：链接和操作
│       ├── 快速链接
│       └── GitHub 链接
├── 分隔线
└── 底部：版权信息
```

### 样式特性
- **背景效果**：`bg-background/95 backdrop-blur`
- **边框**：顶部边框分隔内容区域
- **容器**：使用 container 类实现响应式容器
- **间距**：统一的 padding 和 margin 设计

### 国际化支持
- 支持中英文双语切换
- 根据当前语言显示对应的文本内容
- 链接文本自动适配语言设置

## 使用方法

### 基本使用
```tsx
import { Footer } from '@/components/layout';

export default function Layout({ children }) {
  return (
    <div className="min-h-screen flex flex-col">
      <main className="flex-1">
        {children}
      </main>
      <Footer />
    </div>
  );
}
```

### 在 MainLayout 中的集成
Footer 组件已经集成在 MainLayout 中，无需额外配置：

```tsx
import { MainLayout } from '@/components/layout';

export default function Page() {
  return (
    <MainLayout>
      {/* 页面内容 */}
    </MainLayout>
  );
}
```

## 配置说明

Footer 组件从环境配置中读取以下信息：

### 应用配置 (appConfig)
- `mainTitle`: 主标题
- `suffix`: 后缀
- `version`: 版本号
- `description`: 中文描述
- `descriptionEn`: 英文描述
- `githubUrl`: GitHub 仓库地址

### 环境变量支持
可以通过以下环境变量自定义页脚内容：

```bash
NEXT_PUBLIC_APP_MAIN_TITLE="Your App Name"
NEXT_PUBLIC_APP_SUFFIX="Your Suffix"
NEXT_PUBLIC_APP_VERSION="1.0.0"
NEXT_PUBLIC_APP_DESCRIPTION="您的应用描述"
NEXT_PUBLIC_APP_DESCRIPTION_EN="Your app description"
NEXT_PUBLIC_GITHUB_URL="https://github.com/your-username/your-repo"
```

## 样式定制

### CSS 类名结构
```css
.footer {
  @apply border-t bg-background/95 backdrop-blur;
  @apply supports-[backdrop-filter]:bg-background/60;
  @apply mt-auto;
}

.footer-container {
  @apply container mx-auto px-4 py-6;
}

.footer-content {
  @apply flex flex-col space-y-4;
  @apply md:flex-row md:items-start md:justify-between md:space-y-0;
}
```

### 主题变量
Footer 组件使用以下 CSS 变量：
- `--background`: 背景色
- `--foreground`: 前景色
- `--muted-foreground`: 次要文字颜色
- `--border`: 边框颜色
- `--accent`: 强调色

## 可访问性

### 语义化标签
- 使用 `<footer>` 标签提供语义化结构
- 使用 `role="contentinfo"` 增强可访问性

### 键盘导航
- 所有链接支持键盘导航
- 使用 Tab 键可以依次访问所有可交互元素

### 屏幕阅读器支持
- 提供适当的 aria 标签
- 外部链接包含 `rel="noopener noreferrer"` 属性

## 测试

### 单元测试
组件包含完整的单元测试，覆盖以下场景：
- 基本渲染测试
- 内容显示测试
- 链接功能测试
- 可访问性测试

### 运行测试
```bash
npm test -- footer.test.tsx
```

## 维护说明

### 更新链接
如需修改页脚链接，请编辑 `footerLinks` 数组：

```tsx
const footerLinks = [
  {
    label: '新链接',
    href: 'https://example.com',
    icon: YourIcon,
    external: true,
  },
  // ...其他链接
];
```

### 样式调整
如需调整样式，请修改对应的 Tailwind CSS 类名，保持与项目整体设计的一致性。

### 国际化文本
如需添加新的语言支持，请在翻译钩子中添加对应的文本内容。
