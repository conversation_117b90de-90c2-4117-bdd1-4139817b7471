export const zhCN = {
  // 通用
  common: {
    loading: '加载中...',
    error: '错误',
    success: '成功',
    cancel: '取消',
    confirm: '确认',
    close: '关闭',
    save: '保存',
    delete: '删除',
    edit: '编辑',
    copy: '复制',
    search: '搜索',
    filter: '筛选',
    clear: '清除',
    clearAll: '清除全部',
    all: '全部',
    none: '无',
    yes: '是',
    no: '否',
    back: '返回',
    next: '下一步',
    previous: '上一步',
    submit: '提交',
    reset: '重置',
    duplicate: '复制',
    selectAll: '全选',
    deselectAll: '取消全选',
    saving: '保存中...',
  },

  // 头部导航
  header: {
    toggleSidebar: '切换侧边栏',
    settings: '设置',
    submitColor: '提交颜色',
    totalColors: '总颜色',
    favorites: '收藏',
    switchTheme: '切换主题',
    switchViewMode: '切换视图模式',
  },

  // 主题
  theme: {
    light: '浅色',
    dark: '深色',
    system: '跟随系统',
  },

  // 视图模式
  viewMode: {
    grid: '网格',
    list: '列表',
    compact: '紧凑',
    gridView: '网格视图',
    listView: '列表视图',
    compactView: '紧凑视图',
  },

  // 语言
  language: {
    'zh-CN': '简体中文',
    'en-US': 'English',
  },

  // 设置
  settings: {
    title: '设置',
    theme: '主题',
    themeDescription: '选择界面主题',
    viewMode: '视图模式',
    viewModeDescription: '选择颜色显示方式',
    language: '语言',
    languageDescription: '选择界面语言',
  },

  // 搜索和筛选
  search: {
    placeholder: '搜索颜色...',
    noResults: '未找到匹配的颜色',
    noColors: '暂无颜色',
    noColorsDescription: '开始添加一些颜色来构建您的调色板吧！',
    categories: '分类',
    tags: '标签',
    temperature: '颜色温度',
    favorites: '收藏',
    favoritesOnly: '只显示收藏',
    warm: '暖色',
    cool: '冷色',
    neutral: '中性色',
  },

  // 侧边栏
  sidebar: {
    colorCategories: '颜色分类',
    allColors: '全部颜色',
    recentColors: '最近使用',
    favoriteColors: '收藏颜色',
  },

  // 颜色相关
  color: {
    name: '颜色名称',
    description: '描述',
    hex: 'HEX',
    rgb: 'RGB',
    hsl: 'HSL',
    css: 'CSS变量',
    cssVariable: 'CSS Variable',
    temperature: '颜色温度',
    category: '分类',
    tags: '标签',
    favorite: '收藏',
    unfavorite: '取消收藏',
    copySuccess: '已复制到剪贴板',
    copyFailed: '复制失败',
    similarColors: '相似颜色',
    colorDetails: '颜色详情',
    usageCount: '使用次数',
    usageStats: '使用统计',
    colorFormats: '颜色格式',
    categoriesAndTemperature: '分类和温度',
    isFavorite: '收藏',
    show: '显示',
    hide: '隐藏',
    submitSimilarColor: '提交类似颜色',
    clickToCopy: '点击复制',
    createdAt: '创建时间',
    unknown: '未知',
    brand: '品牌色',
    ui: 'UI色彩',
    team: '团队色彩',
    warm: '暖色',
    cool: '冷色',
    neutral: '中性色',
    copyName: '复制名称',
    addToFavorites: '添加到收藏夹',
    removeFromFavorites: '从收藏夹移除',
    addedToFavorites: '已添加到收藏夹',
    removedFromFavorites: '已从收藏夹移除',
    viewDetails: '查看详情',
    searchInFolder: '在文件夹中搜索颜色...',
    noSearchResults: '未找到匹配的颜色',
    tryDifferentKeyword: '尝试使用不同的关键词',
  },

  // 提交指南
  submitGuide: {
    title: '提交新颜色',
    description:
      '感谢您为 GHS Color 项目贡献新的颜色！请按照以下步骤提交您的颜色建议。',
    steps: {
      title: '提交步骤',
      step1: '访问 GitHub 仓库',
      step1Description: '首先访问我们的 GitHub 仓库了解项目：',
      step2: '创建 Issue',
      step2Description: '在 GitHub 上创建一个新的 Issue 来提交您的颜色：',
      step3: '填写颜色信息',
      step3Description: '按照模板填写完整的颜色信息：',
      step4: '等待审核',
      step4Description: '我们会尽快审核您的提交：',
    },
    colorInfo: {
      title: '颜色信息格式',
      name: '颜色名称（中英文）',
      hex: 'HEX 颜色值',
      description: '颜色描述',
      category: '颜色分类',
      tags: '相关标签',
    },
    requirements: {
      title: '提交要求',
      colorRequirements: '颜色要求',
      colorReq1: '提供有效的 HEX 颜色值',
      colorReq2: '颜色名称要有意义且易懂',
      colorReq3: '避免提交重复的颜色',
      colorReq4: '确保颜色有实际使用价值',
      descriptionRequirements: '描述要求',
      descReq1: '提供中英文名称和描述',
      descReq2: '说明颜色的使用场景',
      descReq3: '选择合适的颜色分类',
      descReq4: '添加相关的标签',
    },
    buttons: {
      visitGitHub: '访问 GitHub 仓库',
      createIssue: '创建颜色提交 Issue',
      contributingGuide: '贡献指南',
      viewIssues: '查看所有 Issues',
    },
    backToColors: '返回颜色管理',
    thanks:
      '感谢您对 GHS Color 项目的贡献！您的每一个颜色建议都让这个工具变得更好。',
  },

  // 工具
  tools: {
    title: '实用工具',
    colorConverter: {
      title: '颜色格式转换',
      description: '在不同颜色格式之间进行转换',
      input: '输入颜色',
      results: '转换结果',
      preview: '颜色预览',
      previewDescription: '当前颜色的实时预览',
      usage: '在上方输入框中输入任意格式的颜色值，系统将自动转换为其他格式。',
      supportedFormats: '支持 HEX、RGB、RGBA、HSL、HSLA、HSV 和 CSS 变量格式。',
      copySuccess: '颜色值已复制',
      copyFailed: '复制失败',
      invalidFormat: '无效的颜色格式',
      invalidValue: '无效值',
      conversionFailed: '转换失败',
      validating: '正在验证...',
    },
    colorConfigGenerator: {
      title: '颜色配置生成器',
      description: '生成符合项目标准的颜色配置JSON',
      form: {
        id: 'ID标识符',
        idPlaceholder: '例如：my-blue',
        idHelp: '唯一标识符，建议使用小写字母和连字符',
        name: '英文名称',
        namePlaceholder: '例如：My Blue',
        nameZh: '中文名称',
        nameZhPlaceholder: '例如：我的蓝色',
        hex: '颜色值',
        hexPlaceholder: '例如：#1f91dc',
        description: '英文描述',
        descriptionPlaceholder: '例如：A beautiful blue color',
        descriptionZh: '中文描述',
        descriptionZhPlaceholder: '例如：一个美丽的蓝色',
        category: '分类',
        categoryPlaceholder: '选择分类',
        tags: '标签',
        tagsPlaceholder: '输入标签，按回车添加',
        tagsHelp: '按回车键添加标签，点击标签可删除',
      },
      preview: {
        title: '颜色预览',
        jsonTitle: '生成的JSON配置',
        copyJson: '复制JSON',
        copySuccess: 'JSON配置已复制到剪贴板',
        copyFailed: '复制失败',
      },
      validation: {
        idRequired: 'ID标识符不能为空',
        idInvalid: 'ID只能包含小写字母、数字和连字符',
        nameRequired: '英文名称不能为空',
        nameZhRequired: '中文名称不能为空',
        hexRequired: '颜色值不能为空',
        hexInvalid: '请输入有效的HEX颜色值',
        descriptionRequired: '英文描述不能为空',
        descriptionZhRequired: '中文描述不能为空',
        categoryRequired: '请选择分类',
        formInvalid: '请先完善表单信息',
      },
      usage: {
        title: '使用说明',
        step1: '1. 填写所有必要的颜色信息',
        step2: '2. 选择合适的颜色分类',
        step3: '3. 添加相关标签（可选）',
        step4: '4. 复制生成的JSON配置',
        step5: '5. 将配置添加到config.js文件中',
      },
    },
  },

  // 错误信息
  error: {
    loadFailed: '加载失败',
    saveFailed: '保存失败',
    networkError: '网络错误',
    unknownError: '未知错误',
    invalidColor: '无效的颜色值',
    invalidFormat: '格式错误',
  },

  // 成功信息
  success: {
    saved: '保存成功',
    copied: '复制成功',
    deleted: '删除成功',
    updated: '更新成功',
  },

  // 文件夹
  folder: {
    // 基本操作
    create: '创建文件夹',
    edit: '编辑文件夹',
    delete: '删除文件夹',
    manageFolders: '管理文件夹',
    addToFolders: '添加到文件夹',
    selectFolders: '选择文件夹',
    selectFoldersForColor: '为颜色选择文件夹',
    selectFoldersDescription: '选择要添加此颜色的文件夹',

    // 文件夹信息
    name: '文件夹名称',
    nameZh: '中文名称',
    description: '描述',
    descriptionZh: '中文描述',
    icon: '图标',
    iconColor: '图标颜色',
    colors: '个颜色',
    system: '系统',
    current: '当前',

    // 占位符
    namePlaceholder: '请输入文件夹名称',
    nameZhPlaceholder: '请输入中文名称',
    descriptionPlaceholder: '请输入描述（可选）',
    descriptionZhPlaceholder: '请输入中文描述（可选）',
    searchPlaceholder: '搜索文件夹...',
    untitled: '未命名文件夹',
    noDescription: '暂无描述',

    // 验证错误
    nameRequired: '文件夹名称不能为空',
    nameZhRequired: '中文名称不能为空',
    nameTooLong: '文件夹名称不能超过50个字符',
    nameZhTooLong: '中文名称不能超过50个字符',
    descriptionTooLong: '描述不能超过200个字符',
    descriptionZhTooLong: '中文描述不能超过200个字符',
    nameExists: '文件夹名称已存在',
    nameZhExists: '中文名称已存在',

    // 状态信息
    noFolders: '暂无文件夹',
    noSearchResults: '未找到匹配的文件夹',
    selectedCount: '已选择 {{count}} 个文件夹',
    oneSelected: '已选择 1 个文件夹',
    noSelection: '未选择文件夹',

    // 操作结果
    createSuccess: '文件夹创建成功',
    createFailed: '文件夹创建失败',
    updateSuccess: '文件夹更新成功',
    updateFailed: '文件夹更新失败',
    deleteSuccess: '文件夹删除成功',
    deleteFailed: '文件夹删除失败',
    deleteConfirm: '确定要删除这个文件夹吗？',
    cannotDeleteSystem: '不能删除系统文件夹',

    // 文件夹视图
    notFound: '文件夹不存在',
    noColors: '此文件夹中暂无颜色',
    addColorsToFolder: '使用右键菜单将颜色添加到此文件夹',
    removeColorConfirm: '确定要从此文件夹中移除这个颜色吗？',
    colorRemoved: '颜色已从文件夹中移除',
    colorAddedToFolder: '颜色已添加到文件夹',
    colorRemovedFromFolder: '颜色已从文件夹中移除',
    addColorFailed: '添加颜色到文件夹失败',
    removeColorFailed: '从文件夹移除颜色失败',

    // 分享功能
    share: {
      title: '分享文件夹',
      generate: '生成分享链接',
      generating: '生成中...',
      regenerate: '重新生成',
      shareUrl: '分享链接',
      copySuccess: '分享链接已复制到剪贴板',
      copyFailed: '复制分享链接失败',
      generateSuccess: '分享链接生成成功',
      generateFailed: '生成分享链接失败',
      qrCode: '二维码',
      qrCodeNote: '扫描二维码访问分享链接',
      downloadQrCode: '下载二维码',
      qrCodeDownload: '二维码下载功能即将推出',
      generatingQrCode: '生成二维码中...',
      qrCodeGenerateFailed: '生成二维码失败',
      qrCodeDownloadSuccess: '二维码下载成功',
      dataSize: '数据大小',
      urlNote: '分享链接包含完整的文件夹数据',
      options: '分享选项',
      includeDescriptions: '包含颜色描述',
      includeTags: '包含标签信息',
      includeUsageStats: '包含使用统计',
    },

    // 导入功能
    import: {
      title: '导入分享文件夹',
      success: '文件夹导入成功，共导入 {{count}} 个颜色',
      failed: '文件夹导入失败',
      importing: '导入中...',
      confirm: '确认导入',
      nameConflict: '文件夹名称 "{{name}}" 已存在',
      conflictStrategy: '冲突处理方式',
      customName: '自定义名称',

      colorPreview: '颜色预览',
      moreColors: '更多颜色',
      strategy: {
        rename: '重命名新文件夹',
        replace: '替换现有文件夹',
        merge: '合并到现有文件夹',
      },
    },
  },
} as const;
