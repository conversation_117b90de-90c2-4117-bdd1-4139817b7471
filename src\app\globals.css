@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 240 10% 3.9%;

    --card: 0 0% 100%;
    --card-foreground: 240 10% 3.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 240 10% 3.9%;

    --primary: 240 5.9% 10%;
    --primary-foreground: 0 0% 98%;

    --secondary: 240 4.8% 95.9%;
    --secondary-foreground: 240 5.9% 10%;

    --muted: 240 4.8% 95.9%;
    --muted-foreground: 240 3.8% 46.1%;

    --accent: 240 4.8% 95.9%;
    --accent-foreground: 240 5.9% 10%;

    --destructive: 0 72.2% 50.6%;
    --destructive-foreground: 0 0% 98%;

    --border: 240 5.9% 90%;
    --input: 240 5.9% 90%;
    --ring: 240 5.9% 10%;

    --radius: 0.5rem;
  }

  .dark {
    --background: 240 10% 3.9%;
    --foreground: 0 0% 98%;

    --card: 240 10% 3.9%;
    --card-foreground: 0 0% 98%;

    --popover: 240 10% 3.9%;
    --popover-foreground: 0 0% 98%;

    --primary: 0 0% 98%;
    --primary-foreground: 240 5.9% 10%;

    --secondary: 240 3.7% 15.9%;
    --secondary-foreground: 0 0% 98%;

    --muted: 240 3.7% 15.9%;
    --muted-foreground: 240 5% 64.9%;

    --accent: 240 3.7% 15.9%;
    --accent-foreground: 0 0% 98%;

    --destructive: 0 72.2% 50.6%;
    --destructive-foreground: 0 0% 98%;

    --border: 240 3.7% 15.9%;
    --input: 240 3.7% 15.9%;
    --ring: 240 4.9% 83.9%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }

  /* Emoji字体支持 */
  .font-emoji {
    font-family: 'Apple Color Emoji', 'Segoe UI Emoji', 'Noto Color Emoji', 'Android Emoji', 'EmojiSymbols', 'EmojiOne Mozilla', 'Twemoji Mozilla', 'Segoe UI Symbol', 'Noto Emoji', sans-serif;
  }
}

/* 自定义滚动条样式 */
@layer utilities {
  /* Webkit 浏览器滚动条样式 */
  ::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }

  ::-webkit-scrollbar-track {
    background: hsl(var(--muted));
    border-radius: 4px;
  }

  ::-webkit-scrollbar-thumb {
    background: hsl(var(--muted-foreground) / 0.3);
    border-radius: 4px;
    transition: background-color 0.2s ease;
  }

  ::-webkit-scrollbar-thumb:hover {
    background: hsl(var(--muted-foreground) / 0.5);
  }

  ::-webkit-scrollbar-thumb:active {
    background: hsl(var(--muted-foreground) / 0.7);
  }

  ::-webkit-scrollbar-corner {
    background: hsl(var(--muted));
  }

  /* Firefox 滚动条样式 */
  * {
    scrollbar-width: thin;
    scrollbar-color: hsl(var(--muted-foreground) / 0.3) hsl(var(--muted));
  }

  /* 深色主题下的滚动条优化 */
  .dark ::-webkit-scrollbar-track {
    background: hsl(var(--muted));
  }

  .dark ::-webkit-scrollbar-thumb {
    background: hsl(var(--muted-foreground) / 0.4);
  }

  .dark ::-webkit-scrollbar-thumb:hover {
    background: hsl(var(--muted-foreground) / 0.6);
  }

  .dark ::-webkit-scrollbar-thumb:active {
    background: hsl(var(--muted-foreground) / 0.8);
  }

  /* 细滚动条变体 */
  .scrollbar-thin::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }

  /* 隐藏滚动条但保持滚动功能 */
  .scrollbar-hide {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }

  .scrollbar-hide::-webkit-scrollbar {
    display: none;
  }

  /* 自定义颜色滚动条 */
  .scrollbar-primary::-webkit-scrollbar-thumb {
    background: hsl(var(--primary) / 0.6);
  }

  .scrollbar-primary::-webkit-scrollbar-thumb:hover {
    background: hsl(var(--primary) / 0.8);
  }

  /* 设置界面优化样式 */
  .settings-section {
    @apply space-y-3;
  }

  .settings-icon-container {
    @apply flex items-center justify-center w-8 h-8 rounded-lg bg-muted/60;
  }

  .settings-button-group {
    @apply flex items-center gap-1 bg-muted/30 rounded-lg p-1 backdrop-blur-sm;
  }

  .settings-button {
    @apply flex items-center gap-2 px-3 py-2 rounded-md text-xs font-medium transition-all flex-1 justify-center;
  }

  .settings-button-active {
    @apply bg-background text-foreground shadow-sm border border-border/50;
  }

  .settings-button-inactive {
    @apply text-muted-foreground hover:text-foreground hover:bg-background/60;
  }

  /* 国旗图标样式 */
  .flag-icon {
    @apply inline-flex items-center justify-center rounded overflow-hidden border border-border/30 shadow-sm;
  }

  .flag-icon:hover {
    @apply border-border/60 shadow-md;
  }

  /* 国旗图标在选择器中的样式 */
  .flag-icon svg {
    @apply w-full h-full object-cover;
  }
}
