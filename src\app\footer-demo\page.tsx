'use client';

import React, { useState } from 'react';
import { MainLayout } from '@/components/layout';
import { <PERSON><PERSON>, Card, CardContent, CardHeader, CardTitle } from '@/components/ui';

export default function FooterDemo() {
  const [contentHeight, setContentHeight] = useState<'short' | 'medium' | 'long'>('medium');

  const shortContent = (
    <div className="space-y-4">
      <h1 className="text-3xl font-bold">页脚演示 - 短内容</h1>
      <p className="text-muted-foreground">
        这是一个短内容页面，用于测试页脚在内容较少时的表现。
      </p>
    </div>
  );

  const mediumContent = (
    <div className="space-y-6">
      <h1 className="text-3xl font-bold">页脚演示 - 中等内容</h1>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {Array.from({ length: 4 }, (_, i) => (
          <Card key={i}>
            <CardHeader>
              <CardTitle>卡片 {i + 1}</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-muted-foreground">
                这是一个示例卡片，用于展示中等长度的内容。页脚应该正确显示在页面底部。
              </p>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  );

  const longContent = (
    <div className="space-y-6">
      <h1 className="text-3xl font-bold">页脚演示 - 长内容</h1>
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {Array.from({ length: 12 }, (_, i) => (
          <Card key={i}>
            <CardHeader>
              <CardTitle>卡片 {i + 1}</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-muted-foreground mb-4">
                这是一个示例卡片，用于展示长内容页面的效果。当内容超过视窗高度时，
                页脚应该在页面底部正确显示，用户需要滚动才能看到。
              </p>
              <p className="text-sm text-muted-foreground">
                页脚组件采用了现代化的设计风格，包含项目信息、快速链接和版权信息。
                它支持响应式设计，在不同屏幕尺寸下都有良好的表现。
              </p>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  );

  const getContent = () => {
    switch (contentHeight) {
      case 'short':
        return shortContent;
      case 'medium':
        return mediumContent;
      case 'long':
        return longContent;
      default:
        return mediumContent;
    }
  };

  return (
    <MainLayout>
      <div className="p-6 space-y-6">
        {/* 控制面板 */}
        <Card>
          <CardHeader>
            <CardTitle>页脚演示控制面板</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex flex-wrap gap-2">
              <Button
                variant={contentHeight === 'short' ? 'default' : 'outline'}
                onClick={() => setContentHeight('short')}
              >
                短内容
              </Button>
              <Button
                variant={contentHeight === 'medium' ? 'default' : 'outline'}
                onClick={() => setContentHeight('medium')}
              >
                中等内容
              </Button>
              <Button
                variant={contentHeight === 'long' ? 'default' : 'outline'}
                onClick={() => setContentHeight('long')}
              >
                长内容
              </Button>
            </div>
            <p className="text-sm text-muted-foreground mt-4">
              切换不同的内容长度来测试页脚在各种情况下的表现。
              页脚应该始终显示在页面底部，不会与主要内容重叠。
            </p>
          </CardContent>
        </Card>

        {/* 动态内容 */}
        {getContent()}

        {/* 页脚测试说明 */}
        <Card>
          <CardHeader>
            <CardTitle>页脚功能测试</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <h4 className="font-semibold mb-2">测试项目：</h4>
              <ul className="list-disc list-inside space-y-1 text-sm text-muted-foreground">
                <li>页脚是否正确显示在页面底部</li>
                <li>响应式设计在不同屏幕尺寸下的表现</li>
                <li>链接是否可以正常点击和跳转</li>
                <li>主题切换时页脚样式是否正确</li>
                <li>多语言切换时文本是否正确显示</li>
              </ul>
            </div>
            <div>
              <h4 className="font-semibold mb-2">预期行为：</h4>
              <ul className="list-disc list-inside space-y-1 text-sm text-muted-foreground">
                <li>短内容时，页脚应该固定在视窗底部</li>
                <li>长内容时，页脚应该在内容末尾，需要滚动查看</li>
                <li>所有链接应该在新标签页中打开（外部链接）</li>
                <li>版本号和项目信息应该正确显示</li>
              </ul>
            </div>
          </CardContent>
        </Card>
      </div>
    </MainLayout>
  );
}
