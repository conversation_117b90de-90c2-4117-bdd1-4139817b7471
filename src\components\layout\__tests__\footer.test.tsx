import { render, screen } from '@testing-library/react';
import { Footer } from '../footer';
import { LanguageProvider } from '@/components/language-provider';
import { ThemeProvider } from '@/components/theme-provider';

// Mock the environment config
jest.mock('@/lib/env-config', () => ({
  appConfig: {
    mainTitle: 'GHS Color',
    suffix: 'Next',
    version: '2.0.0',
    description: '现代化色彩管理工具',
    descriptionEn: 'Modern Color Management Tool',
    githubUrl: 'https://github.com/Mystic-Stars/GHS-Color',
  },
}));

// Mock the translation hook
jest.mock('@/hooks/use-translation', () => ({
  useTranslation: () => ({
    t: (key: string) => key,
    language: 'zh',
  }),
}));

const renderWithProviders = (component: React.ReactElement) => {
  return render(
    <ThemeProvider defaultTheme="light" storageKey="test-theme">
      <LanguageProvider>
        {component}
      </LanguageProvider>
    </ThemeProvider>
  );
};

describe('Footer Component', () => {
  it('renders project title and version', () => {
    renderWithProviders(<Footer />);
    
    expect(screen.getByText('GHS Color')).toBeInTheDocument();
    expect(screen.getByText('Next')).toBeInTheDocument();
    expect(screen.getByText('v2.0.0')).toBeInTheDocument();
  });

  it('renders project description', () => {
    renderWithProviders(<Footer />);
    
    expect(screen.getByText('现代化色彩管理工具')).toBeInTheDocument();
  });

  it('renders footer links', () => {
    renderWithProviders(<Footer />);
    
    expect(screen.getByText('文档')).toBeInTheDocument();
    expect(screen.getByText('问题反馈')).toBeInTheDocument();
    expect(screen.getByText('贡献指南')).toBeInTheDocument();
  });

  it('renders GitHub link', () => {
    renderWithProviders(<Footer />);
    
    const githubLink = screen.getByText('查看源码');
    expect(githubLink).toBeInTheDocument();
    expect(githubLink.closest('a')).toHaveAttribute('href', 'https://github.com/Mystic-Stars/GHS-Color');
  });

  it('renders copyright information', () => {
    renderWithProviders(<Footer />);
    
    const currentYear = new Date().getFullYear();
    expect(screen.getByText(`© ${currentYear}`)).toBeInTheDocument();
    expect(screen.getByText('用')).toBeInTheDocument();
    expect(screen.getByText('制作')).toBeInTheDocument();
  });

  it('has proper accessibility attributes', () => {
    renderWithProviders(<Footer />);
    
    const footer = screen.getByRole('contentinfo');
    expect(footer).toBeInTheDocument();
  });
});
