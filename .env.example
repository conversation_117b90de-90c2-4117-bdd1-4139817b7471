# GHS Color Next - 环境变量配置模板
# 复制此文件为 .env.local 并根据需要修改配置

# ===========================================
# 应用基本信息配置
# ===========================================

# 应用名称
NEXT_PUBLIC_APP_NAME=GHS Color Next

# 应用主标题和后缀（用于动态标题显示）
NEXT_PUBLIC_APP_MAIN_TITLE=GHS Color
NEXT_PUBLIC_APP_SUFFIX=Next

# 应用版本
NEXT_PUBLIC_APP_VERSION=2.0.0

# 应用描述
NEXT_PUBLIC_APP_DESCRIPTION=现代化色彩管理工具
NEXT_PUBLIC_APP_DESCRIPTION_EN=Modern Color Management Tool

# GitHub 仓库地址
NEXT_PUBLIC_GITHUB_URL=https://github.com/Mystic-Stars/GHS-Color

# ===========================================
# SEO 页面配置
# ===========================================

# 页面标题 - 中文
NEXT_PUBLIC_SITE_TITLE=GHS Color Next - 现代化色彩管理工具

# 页面描述 - 中文
NEXT_PUBLIC_SITE_DESCRIPTION=基于原版GHS Color重构的现代化色彩管理工具

# 页面标题 - 英文
NEXT_PUBLIC_SITE_TITLE_EN=GHS Color Next - Modern Color Management Tool

# 页面描述 - 英文
NEXT_PUBLIC_SITE_DESCRIPTION_EN=A modern color management tool based on the original GHS Color, supporting multiple color formats, theme switching, multi-language and more

# 关键字（SEO）
NEXT_PUBLIC_SITE_KEYWORDS=GHS Color,color management,color tool,design tool,color picker,palette,颜色管理,色彩工具

# ===========================================
# Docker 部署配置
# ===========================================

# 容器端口（默认3000）
PORT=3000

# 主机名（Docker内部使用）
HOSTNAME=0.0.0.0

# Node.js 环境
NODE_ENV=production

# ===========================================
# 颜色数据配置（可选）
# ===========================================

# 颜色数据 - JSON格式
# 如果设置了此环境变量，将优先使用环境变量中的颜色数据，而不是config.js文件
# 格式：JSON数组，每个对象包含id、name、nameZh、hex、description、descriptionZh、category、tags等字段
# NEXT_PUBLIC_COLORS=[{"id":"custom-red","name":"Custom Red","nameZh":"自定义红色","hex":"#ff0000","description":"A custom red color","descriptionZh":"自定义的红色","category":"brand","tags":["red","custom"]}]

# 分类数据 - JSON格式
# 如果设置了此环境变量，将优先使用环境变量中的分类数据，而不是config.js文件
# 格式：JSON数组，每个对象包含id、name、nameZh、description、icon、color、order等字段
# NEXT_PUBLIC_CATEGORIES=[{"id":"custom","name":"Custom Colors","nameZh":"自定义颜色","description":"Custom color category","icon":"🎨","color":"#6366F1","order":1}]

# ===========================================
# 可选配置
# ===========================================

# 自定义配置键（如需要）
CUSTOM_KEY=your_custom_value_here

# 禁用 Next.js 遥测
NEXT_TELEMETRY_DISABLED=1
