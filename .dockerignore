# GHS Color Next - Docker忽略文件
# 优化Docker构建性能，排除不必要的文件

# 依赖目录
node_modules
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Next.js构建输出
.next/
out/
build/

# 生产构建
/build

# 运行时数据
pids
*.pid
*.seed
*.pid.lock

# 覆盖率目录
coverage/
.nyc_output

# 环境变量文件
.env*.local
.env.development
.env.test
.env.production

# Vercel
.vercel

# TypeScript
*.tsbuildinfo
next-env.d.ts

# IDE和编辑器
.vscode/
.idea/
*.swp
*.swo
*~

# 操作系统
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# 日志文件
logs
*.log

# 测试
/coverage
.nyc_output

# 缓存
.cache/
.parcel-cache/

# Git
.git
.gitignore

# Docker
Dockerfile*
docker-compose*
.dockerignore

# 文档
README.md
docs/
*.md

# 许可证
LICENSE

# 脚本
scripts/

# 测试文件
tests/
__tests__/
*.test.js
*.test.ts
*.test.tsx
*.spec.js
*.spec.ts
*.spec.tsx

# 配置文件（保留必要的）
jest.config.js
jest.setup.js
.eslintrc*
.prettierrc*
.commitlintrc*
.husky/

# 临时文件
tmp/
temp/
